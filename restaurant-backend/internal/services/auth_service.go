package services

import (
	"context"
	"crypto/sha256"
	"fmt"
	"strings"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/types"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

// AuthService handles authentication business logic
type AuthService struct {
	userRepo  repositories.UserRepository
	secretKey string
	expiresIn time.Duration
}

// NewAuthService creates a new auth service
func NewAuthService(userRepo repositories.UserRepository, secretKey string, expiresIn time.Duration) *AuthService {
	return &AuthService{
		userRepo:  userRepo,
		secretKey: secretKey,
		expiresIn: expiresIn,
	}
}

// Login authenticates a user and returns a JWT token
func (s *AuthService) Login(ctx context.Context, req types.LoginRequest) (*types.LoginResponse, error) {
	// This is a placeholder implementation
	// In a real implementation, you would:
	// 1. Find user by email
	// 2. Verify password
	// 3. Generate JWT token
	// 4. Return response

	// For now, return a mock response
	user := &models.User{
		BaseModel: models.BaseModel{ID: uuid.New()},
		Email:     req.Email,
		FirstName: "Demo",
		LastName:  "User",
	}

	token, err := s.generateToken(user)
	if err != nil {
		return nil, err
	}

	return &types.LoginResponse{
		Token:     token,
		User:      user,
		ExpiresAt: time.Now().Add(s.expiresIn),
	}, nil
}

// Register creates a new user account
func (s *AuthService) Register(ctx context.Context, req types.RegisterRequest) (*types.RegisterResponse, error) {
	// This is a placeholder implementation
	// In a real implementation, you would:
	// 1. Check if user already exists
	// 2. Hash password
	// 3. Create user in database
	// 4. Return response

	user := &models.User{
		BaseModel: models.BaseModel{ID: uuid.New()},
		Email:     req.Email,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Phone:     req.Phone,
	}

	return &types.RegisterResponse{
		User:    user,
		Message: "User registered successfully",
	}, nil
}

// RefreshToken refreshes an expired JWT token
func (s *AuthService) RefreshToken(ctx context.Context, req types.RefreshTokenRequest) (*types.LoginResponse, error) {
	// This is a placeholder implementation
	// In a real implementation, you would:
	// 1. Validate refresh token
	// 2. Get user from refresh token
	// 3. Generate new JWT token
	// 4. Return response

	return &types.LoginResponse{
		Token:     "new-jwt-token",
		ExpiresAt: time.Now().Add(s.expiresIn),
	}, nil
}

// GetCurrentUser returns the current authenticated user
func (s *AuthService) GetCurrentUser(ctx context.Context, userID interface{}) (*models.User, error) {
	// This is a placeholder implementation
	// In a real implementation, you would:
	// 1. Get user ID from context
	// 2. Fetch user from database
	// 3. Return user

	var uid uuid.UUID

	// Handle both string (NextAuth) and UUID (legacy JWT) formats
	switch v := userID.(type) {
	case string:
		// Try to parse as UUID for database operations
		if parsedUUID, err := uuid.Parse(v); err == nil {
			uid = parsedUUID
		} else {
			// For OAuth provider IDs, generate a deterministic UUID
			// In a real implementation, you'd have a user mapping table
			uid = uuid.New() // This should be replaced with proper user lookup
		}
	case uuid.UUID:
		uid = v
	default:
		return nil, fmt.Errorf("invalid user ID format")
	}

	user := &models.User{
		BaseModel: models.BaseModel{ID: uid},
		Email:     "<EMAIL>",
		FirstName: "Demo",
		LastName:  "User",
	}

	return user, nil
}

// CreateOAuthUser creates or updates a user from OAuth provider
func (s *AuthService) CreateOAuthUser(ctx context.Context, req types.CreateOAuthUserRequest) (*types.UserResponse, error) {
	// For now, return a simple response indicating OAuth user creation is handled elsewhere
	// The actual OAuth user creation should be handled during the login process
	// This endpoint is mainly for the frontend to call during OAuth callback

	// Parse name fields
	firstName := req.FirstName
	lastName := req.LastName
	if firstName == "" && lastName == "" && req.Name != "" {
		names := strings.Fields(req.Name)
		if len(names) > 0 {
			firstName = names[0]
		}
		if len(names) > 1 {
			lastName = strings.Join(names[1:], " ")
		}
	}

	// Generate a deterministic UUID from OAuth user ID
	userID := generateDeterministicUUID(req.OAuthUserID)

	// Return a mock response for now
	// In a real implementation, this would create the user in the database
	response := &types.UserResponse{
		ID:        userID,
		ShopID:    uuid.New(), // Mock shop ID
		Email:     req.Email,
		FirstName: firstName,
		LastName:  lastName,
		AvatarURL: req.AvatarURL,
		RoleID:    uuid.New(), // Mock role ID
		Role: &types.RoleResponse{
			ID:   uuid.New(),
			Name: "user",
		},
		Status:    types.UserStatusActive,
		HireDate:  time.Now(),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	return response, nil
}

// generateToken generates a JWT token for a user
func (s *AuthService) generateToken(user *models.User) (string, error) {
	// Handle nil BranchID by using a zero UUID
	var branchID uuid.UUID
	if user.BranchID != nil {
		branchID = *user.BranchID
	}

	claims := &types.Claims{
		UserID:   user.ID,
		ShopID:   user.ShopID,
		BranchID: branchID,
		Role:     user.Role.Name,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(s.expiresIn)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Issuer:    "restaurant-api",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.secretKey))
}

// generateDeterministicUUID generates a deterministic UUID from a string
func generateDeterministicUUID(input string) uuid.UUID {
	// Use SHA-256 to hash the input string
	hash := sha256.Sum256([]byte(input))

	// Use the first 16 bytes of the hash to create a UUID
	var uuidBytes [16]byte
	copy(uuidBytes[:], hash[:16])

	// Set version (4) and variant bits according to RFC 4122
	uuidBytes[6] = (uuidBytes[6] & 0x0f) | 0x40 // Version 4
	uuidBytes[8] = (uuidBytes[8] & 0x3f) | 0x80 // Variant 10

	return uuid.UUID(uuidBytes)
}
