package database

import (
	"errors"
	"fmt"
	"math/rand"
	"time"

	"restaurant-backend/internal/models"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Initialize initializes the database connection
func Initialize(dsn string, log *logrus.Logger) (*gorm.DB, error) {
	// Determine GORM log level based on application log level
	var gormLogLevel logger.LogLevel
	switch log.Level {
	case logrus.DebugLevel:
		gormLogLevel = logger.Info // Show all queries in debug mode
	case logrus.InfoLevel:
		gormLogLevel = logger.Warn // Only show warnings and errors in info mode
	case logrus.WarnLevel:
		gormLogLevel = logger.Error // Only show errors in warn mode
	case logrus.ErrorLevel, logrus.FatalLevel, logrus.PanicLevel:
		gormLogLevel = logger.Silent // Silent in error/fatal/panic modes
	default:
		gormLogLevel = logger.Warn // Default to warn level
	}

	// Configure GORM logger
	gormLogger := logger.New(
		log,
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  gormLogLevel,
			IgnoreRecordNotFoundError: true,
			Colorful:                  false,
		},
	)

	// Open database connection
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: gormLogger,
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
	})
	if err != nil {
		return nil, err
	}

	// Configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	// Set connection pool settings
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	// Test the connection
	if err := sqlDB.Ping(); err != nil {
		return nil, err
	}

	log.Info("Database connection established successfully")
	return db, nil
}

// Migrate runs database migrations
func Migrate(db *gorm.DB) error {
	// Enable UUID extension
	if err := db.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"").Error; err != nil {
		return err
	}

	// Fix data integrity issues before migration
	if err := fixDataIntegrity(db); err != nil {
		return err
	}

	// Auto-migrate all models
	err := db.AutoMigrate(
		&models.User{},
		&models.Role{},
		&models.MenuCategory{},
		&models.MenuItem{},
		&models.Order{},
		&models.OrderItem{},
		&models.Payment{},
		&models.Reservation{},
		&models.Table{},
		&models.TableArea{},
		&models.Floor{},
		&models.Review{},

		// New models for services, shops, and campaigns
		&models.Shop{},
		&models.ShopBranch{},
		&models.Service{},
		&models.Staff{},
		&models.Appointment{},
		&models.StaffService{},
		&models.ServiceAvailability{},
		&models.CommunicationTemplate{},
		&models.CampaignSegment{},
		&models.CommunicationCampaign{},
		&models.CommunicationAnalytics{},

		// Notification model
		&models.Notification{},

		// Analytics models
		&models.SalesMetric{},
		&models.CustomerMetric{},
		&models.MenuItemMetric{},
		&models.StaffMetric{},
		&models.TableMetric{},
		&models.AnalyticsReport{},
	)
	if err != nil {
		return err
	}

	// Create indexes for better performance
	if err := createIndexes(db); err != nil {
		return err
	}

	// Seed default data
	if err := seedDefaultData(db); err != nil {
		return err
	}

	// Create default floors and areas for existing branches
	if err := createDefaultFloorsAndAreas(db); err != nil {
		return err
	}

	return nil
}

// fixDataIntegrity fixes data integrity issues before running migrations
func fixDataIntegrity(db *gorm.DB) error {
	// Check if shops table exists
	if !db.Migrator().HasTable(&models.Shop{}) {
		return nil // Table doesn't exist yet, no need to fix
	}

	// Check if users table exists
	if !db.Migrator().HasTable(&models.User{}) {
		return nil // Users table doesn't exist yet, no need to fix
	}

	// Check if there are any orphaned shops
	var orphanedCount int64
	err := db.Raw(`
		SELECT COUNT(*)
		FROM shops s
		LEFT JOIN users u ON s.owner_id = u.id
		WHERE u.id IS NULL AND s.owner_id IS NOT NULL
	`).Scan(&orphanedCount).Error
	if err != nil {
		return err
	}

	// Check for shops with NULL owner_id
	var nullOwnerCount int64
	err = db.Raw(`
		SELECT COUNT(*)
		FROM shops s
		WHERE s.owner_id IS NULL
	`).Scan(&nullOwnerCount).Error
	if err != nil {
		return err
	}

	// If no orphaned shops, no need to fix
	if orphanedCount == 0 && nullOwnerCount == 0 {
		return nil
	}

	// Get the first existing user to use as system owner
	var firstUser models.User
	result := db.First(&firstUser)
	if result.Error != nil {
		return result.Error // No users exist, cannot fix
	}

	// Update orphaned shops to use the first existing user
	if orphanedCount > 0 {
		updateQuery := `
			UPDATE shops
			SET owner_id = ?, updated_at = NOW()
			WHERE owner_id IS NOT NULL
			AND owner_id NOT IN (SELECT id FROM users)
		`

		if err := db.Exec(updateQuery, firstUser.ID).Error; err != nil {
			return err
		}
	}

	// Handle shops with NULL owner_id
	if nullOwnerCount > 0 {
		nullOwnerQuery := `
			UPDATE shops
			SET owner_id = ?, updated_at = NOW()
			WHERE owner_id IS NULL
		`

		if err := db.Exec(nullOwnerQuery, firstUser.ID).Error; err != nil {
			return err
		}
	}

	return nil
}

// createIndexes creates additional database indexes for performance
func createIndexes(db *gorm.DB) error {
	indexes := []string{
		// Orders indexes
		"CREATE INDEX IF NOT EXISTS idx_orders_branch_status ON orders(branch_id, status)",
		"CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at)",
		"CREATE INDEX IF NOT EXISTS idx_orders_customer_phone ON orders(customer_phone)",

		// Menu items indexes
		"CREATE INDEX IF NOT EXISTS idx_menu_items_branch_category ON menu_items(branch_id, category_id)",
		"CREATE INDEX IF NOT EXISTS idx_menu_items_available ON menu_items(branch_id) WHERE is_available = true",
		"CREATE INDEX IF NOT EXISTS idx_menu_items_slug ON menu_items(branch_id, slug)",
		"CREATE INDEX IF NOT EXISTS idx_menu_items_branch_name ON menu_items(branch_id, name)",
		"CREATE INDEX IF NOT EXISTS idx_menu_items_branch_deleted ON menu_items(branch_id, deleted_at)",

		// Reservations indexes
		"CREATE INDEX IF NOT EXISTS idx_reservations_branch_date ON reservations(branch_id, reservation_date)",
		"CREATE INDEX IF NOT EXISTS idx_reservations_status ON reservations(branch_id, status)",

		// Reviews indexes
		"CREATE INDEX IF NOT EXISTS idx_reviews_branch_rating ON reviews(branch_id, rating)",
		"CREATE INDEX IF NOT EXISTS idx_reviews_created_at ON reviews(created_at)",

		// Tables indexes
		"CREATE INDEX IF NOT EXISTS idx_tables_branch_status ON tables(branch_id, status)",
		"CREATE INDEX IF NOT EXISTS idx_tables_active ON tables(branch_id) WHERE is_active = true",

		// Users indexes
		"CREATE INDEX IF NOT EXISTS idx_users_branch_status ON users(branch_id, status)",
		"CREATE INDEX IF NOT EXISTS idx_users_role ON users(role_id)",
	}

	for _, index := range indexes {
		if err := db.Exec(index).Error; err != nil {
			return err
		}
	}

	return nil
}

// seedDefaultData seeds the database with default data
func seedDefaultData(db *gorm.DB) error {
	// Check if we already have shop data
	var shopCount int64
	db.Model(&models.Shop{}).Count(&shopCount)

	// Check if we have menu items for the thai-delight shop
	var menuItemCount int64
	db.Raw(`
		SELECT COUNT(mi.*)
		FROM menu_items mi
		JOIN shop_branches sb ON mi.branch_id = sb.id
		JOIN shops s ON sb.shop_id = s.id
		WHERE s.slug = 'thai-delight'
	`).Scan(&menuItemCount)

	// If we have shops but no menu items, seed the menu items
	if shopCount > 0 && menuItemCount == 0 {
		// Get the existing shop and branch
		var shop models.Shop
		if err := db.Where("slug = ?", "thai-delight").First(&shop).Error; err != nil {
			return err
		}

		var branch models.ShopBranch
		if err := db.Where("shop_id = ? AND slug = ?", shop.ID, "downtown").First(&branch).Error; err != nil {
			return err
		}

		// Seed menu items only
		if err := seedMenuItems(db, shop.ID.String(), branch.ID.String()); err != nil {
			return err
		}

		return nil
	}

	// If we have shops and menu items, skip seeding
	if shopCount > 0 && menuItemCount > 0 {
		return nil // Data already exists
	}

	// Create default permissions
	if err := seedDefaultRoles(db); err != nil {
		return err
	}

	// Create demo shop and branch data
	if err := seedDemoData(db); err != nil {
		return err
	}

	return nil
}

// seedMenuItems creates menu items for an existing shop and branch
func seedMenuItems(db *gorm.DB, shopID, branchID string) error {
	// Parse UUIDs from strings
	branchUUID, err := uuid.Parse(branchID)
	if err != nil {
		return err
	}

	// Create sample menu categories
	categories := []*models.MenuCategory{
		{
			BranchID:    branchUUID,
			Name:        "Appetizers",
			Slug:        "appetizers",
			Description: "Start your meal with our delicious appetizers",
			SortOrder:   1,
			IsActive:    true,
		},
		{
			BranchID:    branchUUID,
			Name:        "Main Courses",
			Slug:        "main-courses",
			Description: "Our signature main dishes",
			SortOrder:   2,
			IsActive:    true,
		},
		{
			BranchID:    branchUUID,
			Name:        "Desserts",
			Slug:        "desserts",
			Description: "Sweet endings to your meal",
			SortOrder:   3,
			IsActive:    true,
		},
		{
			BranchID:    branchUUID,
			Name:        "Beverages",
			Slug:        "beverages",
			Description: "Refreshing drinks and beverages",
			SortOrder:   4,
			IsActive:    true,
		},
	}

	for _, category := range categories {
		if err := db.Create(category).Error; err != nil {
			return err
		}
	}

	// Create comprehensive sample menu items with full data
	menuItems := []*models.MenuItem{
		{
			BranchID:    branchUUID,
			CategoryID:  &categories[0].ID, // Appetizers
			Name:        "Caesar Salad",
			Slug:        "caesar-salad",
			Description: "Fresh romaine lettuce with parmesan cheese, croutons, and our signature Caesar dressing",
			Price:       12.99,
			Cost:        floatPtr(6.50),
			Images:      models.ImagesData{"https://images.unsplash.com/photo-1546793665-c74683f339c1?q=80&w=1000&auto=format&fit=crop", "https://images.unsplash.com/photo-1512621776951-a57141f2eefd?q=80&w=1000&auto=format&fit=crop"},
			Ingredients: models.IngredientsData{"romaine lettuce", "parmesan cheese", "croutons", "caesar dressing", "anchovies", "garlic"},
			Allergens:   models.AllergensData{"dairy", "gluten", "fish"},
			NutritionalInfo: models.NutritionalInfoData{
				Calories: 280,
				Protein:  12.5,
				Carbs:    18.0,
				Fat:      20.0,
				Fiber:    4.2,
				Sodium:   650.0,
			},
			PreparationTime: intPtr(10),
			IsAvailable:     true,
			IsVegetarian:    true,
			IsVegan:         false,
			IsGlutenFree:    false,
			IsSpicy:         false,
			SpiceLevel:      0,
			Tags:            models.TagsData{"popular", "healthy", "classic"},
		},
		{
			BranchID:    branchUUID,
			CategoryID:  &categories[1].ID, // Main Courses
			Name:        "Grilled Salmon",
			Slug:        "grilled-salmon",
			Description: "Fresh Atlantic salmon grilled to perfection, served with seasonal vegetables and lemon herb butter",
			Price:       24.99,
			Cost:        floatPtr(12.00),
			Images:      models.ImagesData{"https://images.unsplash.com/photo-1467003909585-2f8a72700288?q=80&w=1000&auto=format&fit=crop", "https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?q=80&w=1000&auto=format&fit=crop"},
			Ingredients: models.IngredientsData{"atlantic salmon", "seasonal vegetables", "lemon", "herbs", "butter", "olive oil"},
			Allergens:   models.AllergensData{"fish", "dairy"},
			NutritionalInfo: models.NutritionalInfoData{
				Calories: 420,
				Protein:  35.0,
				Carbs:    8.0,
				Fat:      28.0,
				Fiber:    3.5,
				Sodium:   380.0,
			},
			PreparationTime: intPtr(20),
			IsAvailable:     true,
			IsVegetarian:    false,
			IsVegan:         false,
			IsGlutenFree:    true,
			IsSpicy:         false,
			SpiceLevel:      0,
			Tags:            models.TagsData{"signature", "healthy", "protein-rich"},
		},
		{
			BranchID:    branchUUID,
			CategoryID:  &categories[1].ID, // Main Courses
			Name:        "Pad Thai",
			Slug:        "pad-thai",
			Description: "Traditional Thai stir-fried rice noodles with shrimp, tofu, bean sprouts, and our authentic tamarind sauce",
			Price:       18.99,
			Cost:        floatPtr(8.50),
			Images:      models.ImagesData{"https://images.unsplash.com/photo-**********-0f31657def5e?q=80&w=1000&auto=format&fit=crop", "https://images.unsplash.com/photo-1582878826629-29b7ad1cdc43?q=80&w=1000&auto=format&fit=crop"},
			Ingredients: models.IngredientsData{"rice noodles", "shrimp", "tofu", "bean sprouts", "eggs", "tamarind sauce", "fish sauce", "palm sugar", "peanuts", "lime"},
			Allergens:   models.AllergensData{"shellfish", "soy", "eggs", "peanuts", "fish"},
			NutritionalInfo: models.NutritionalInfoData{
				Calories: 380,
				Protein:  22.0,
				Carbs:    45.0,
				Fat:      12.0,
				Fiber:    3.8,
				Sodium:   890.0,
			},
			PreparationTime: intPtr(15),
			IsAvailable:     true,
			IsVegetarian:    false,
			IsVegan:         false,
			IsGlutenFree:    true,
			IsSpicy:         true,
			SpiceLevel:      2,
			Tags:            models.TagsData{"signature", "traditional", "spicy", "popular"},
		},
		{
			BranchID:    branchUUID,
			CategoryID:  &categories[2].ID, // Desserts
			Name:        "Chocolate Lava Cake",
			Slug:        "chocolate-lava-cake",
			Description: "Warm chocolate cake with a molten center, served with vanilla ice cream and fresh berries",
			Price:       8.99,
			Cost:        floatPtr(3.50),
			Images:      models.ImagesData{"https://images.unsplash.com/photo-1606313564200-e75d5e30476c?q=80&w=1000&auto=format&fit=crop", "https://images.unsplash.com/photo-1578985545062-69928b1d9587?q=80&w=1000&auto=format&fit=crop"},
			Ingredients: models.IngredientsData{"dark chocolate", "butter", "eggs", "sugar", "flour", "vanilla ice cream", "mixed berries"},
			Allergens:   models.AllergensData{"dairy", "eggs", "gluten"},
			NutritionalInfo: models.NutritionalInfoData{
				Calories: 450,
				Protein:  8.0,
				Carbs:    52.0,
				Fat:      24.0,
				Fiber:    4.0,
				Sodium:   180.0,
			},
			PreparationTime: intPtr(12),
			IsAvailable:     true,
			IsVegetarian:    true,
			IsVegan:         false,
			IsGlutenFree:    false,
			IsSpicy:         false,
			SpiceLevel:      0,
			Tags:            models.TagsData{"popular", "sweet", "indulgent", "warm"},
		},
		{
			BranchID:    branchUUID,
			CategoryID:  &categories[3].ID, // Beverages
			Name:        "Thai Iced Tea",
			Slug:        "thai-iced-tea",
			Description: "Traditional Thai tea with condensed milk, served over ice with a creamy finish",
			Price:       4.99,
			Cost:        floatPtr(1.50),
			Images:      models.ImagesData{"https://images.unsplash.com/photo-1571934811356-5cc061b6821f?q=80&w=1000&auto=format&fit=crop"},
			Ingredients: models.IngredientsData{"thai tea leaves", "condensed milk", "evaporated milk", "sugar", "ice"},
			Allergens:   models.AllergensData{"dairy"},
			NutritionalInfo: models.NutritionalInfoData{
				Calories: 180,
				Protein:  4.0,
				Carbs:    32.0,
				Fat:      4.5,
				Fiber:    0.0,
				Sodium:   65.0,
			},
			PreparationTime: intPtr(3),
			IsAvailable:     true,
			IsVegetarian:    true,
			IsVegan:         false,
			IsGlutenFree:    true,
			IsSpicy:         false,
			SpiceLevel:      0,
			Tags:            models.TagsData{"traditional", "refreshing", "sweet", "cold"},
		},
	}

	for _, item := range menuItems {
		if err := db.Create(item).Error; err != nil {
			return err
		}
	}

	return nil
}

// seedDefaultRoles creates default roles and permissions
func seedDefaultRoles(db *gorm.DB) error {
	// This will be implemented when we create the demo data
	return nil
}

// createDefaultFloorsAndAreas creates default floor and area for branches that don't have them
func createDefaultFloorsAndAreas(db *gorm.DB) error {
	// Get all branches that don't have floors
	var branches []models.ShopBranch
	err := db.Raw(`
		SELECT sb.* FROM shop_branches sb
		LEFT JOIN floors f ON sb.id = f.branch_id AND f.is_active = true
		WHERE f.id IS NULL
	`).Scan(&branches).Error
	if err != nil {
		return err
	}

	for _, branch := range branches {
		// Create default floor
		floor := models.Floor{
			BranchID:    branch.ID,
			Name:        "Ground Floor",
			Description: "Main dining area",
			Order:       1,
			Layout: models.FloorLayout{
				Width:    800,
				Height:   600,
				GridSize: 20,
				ShowGrid: true,
			},
			IsActive: true,
		}

		if err := db.Create(&floor).Error; err != nil {
			return err
		}

		// Create default area
		area := models.TableArea{
			BranchID:    branch.ID,
			FloorID:     &floor.ID,
			Name:        "Main Dining Area",
			Description: "Primary seating area",
			Color:       "#8a745c",
			IsActive:    true,
		}

		if err := db.Create(&area).Error; err != nil {
			return err
		}
	}

	return nil
}

// seedDemoData creates demo shop and sample data
func seedDemoData(db *gorm.DB) error {
	// Create the shop data that the frontend expects
	if err := seedShopData(db); err != nil {
		return err
	}

	// Create additional shop data for testing
	if err := seedAdditionalShopData(db); err != nil {
		return err
	}

	// Get the created shop and branch for further seeding
	var shop models.Shop
	if err := db.Where("slug = ?", "thai-delight").First(&shop).Error; err != nil {
		return err
	}

	var branch models.ShopBranch
	if err := db.Where("shop_id = ? AND slug = ?", shop.ID, "downtown").First(&branch).Error; err != nil {
		return err
	}

	// Seed staff members
	if err := seedStaffData(db, shop.ID, branch.ID); err != nil {
		return err
	}

	// Create default admin role
	adminRole := &models.Role{
		ShopID:      shop.ID,
		Name:        "Administrator",
		Description: "Full access to all features",
		Permissions: models.PermissionsData{
			models.PermissionViewDashboard,
			models.PermissionManageOrders,
			models.PermissionManageMenu,
			models.PermissionManageStaff,
			models.PermissionManageReservations,
			models.PermissionManageTables,
			models.PermissionManageReviews,
			models.PermissionViewReports,
			models.PermissionManageSettings,
		},
		IsActive: true,
	}

	if err := db.Create(adminRole).Error; err != nil {
		return err
	}

	// Create demo admin user
	adminUser := &models.User{
		ShopID:     shop.ID,
		BranchID:   &branch.ID,
		Email:      "<EMAIL>",
		FirstName:  "Admin",
		LastName:   "User",
		Phone:      "+66 2 123 4567",
		RoleID:     adminRole.ID,
		Position:   "General Manager",
		Department: "Management",
		EmployeeID: "EMP001",
		Status:     models.UserStatusActive,
		HireDate:   time.Now().AddDate(-1, 0, 0), // Hired 1 year ago
	}

	// Set default password
	if err := adminUser.SetPassword("admin123"); err != nil {
		return err
	}

	if err := db.Create(adminUser).Error; err != nil {
		return err
	}

	// Create sample menu categories
	categories := []*models.MenuCategory{
		{
			BranchID:    branch.ID,
			Name:        "Appetizers",
			Slug:        "appetizers",
			Description: "Start your meal with our delicious appetizers",
			SortOrder:   1,
			IsActive:    true,
		},
		{
			BranchID:    branch.ID,
			Name:        "Main Courses",
			Slug:        "main-courses",
			Description: "Our signature main dishes",
			SortOrder:   2,
			IsActive:    true,
		},
		{
			BranchID:    branch.ID,
			Name:        "Desserts",
			Slug:        "desserts",
			Description: "Sweet endings to your meal",
			SortOrder:   3,
			IsActive:    true,
		},
		{
			BranchID:    branch.ID,
			Name:        "Beverages",
			Slug:        "beverages",
			Description: "Refreshing drinks and beverages",
			SortOrder:   4,
			IsActive:    true,
		},
	}

	for _, category := range categories {
		if err := db.Create(category).Error; err != nil {
			return err
		}
	}

	// Create comprehensive sample menu items with full data
	menuItems := []*models.MenuItem{
		{
			BranchID:    branch.ID,
			CategoryID:  &categories[0].ID, // Appetizers
			Name:        "Caesar Salad",
			Slug:        "caesar-salad",
			Description: "Fresh romaine lettuce with parmesan cheese, croutons, and our signature Caesar dressing",
			Price:       12.99,
			Cost:        floatPtr(6.50),
			Images:      models.ImagesData{"https://images.unsplash.com/photo-1546793665-c74683f339c1?q=80&w=1000&auto=format&fit=crop", "https://images.unsplash.com/photo-1512621776951-a57141f2eefd?q=80&w=1000&auto=format&fit=crop"},
			Ingredients: models.IngredientsData{"romaine lettuce", "parmesan cheese", "croutons", "caesar dressing", "anchovies", "garlic"},
			Allergens:   models.AllergensData{"dairy", "gluten", "fish"},
			NutritionalInfo: models.NutritionalInfoData{
				Calories: 280,
				Protein:  12.5,
				Carbs:    18.0,
				Fat:      20.0,
				Fiber:    4.2,
				Sodium:   650.0,
			},
			PreparationTime: intPtr(10),
			IsAvailable:     true,
			IsVegetarian:    true,
			IsVegan:         false,
			IsGlutenFree:    false,
			IsSpicy:         false,
			SpiceLevel:      0,
			Tags:            models.TagsData{"popular", "healthy", "classic"},
		},
		{
			BranchID:    branch.ID,
			CategoryID:  &categories[1].ID, // Main Courses
			Name:        "Grilled Salmon",
			Slug:        "grilled-salmon",
			Description: "Fresh Atlantic salmon grilled to perfection, served with seasonal vegetables and lemon herb butter",
			Price:       24.99,
			Cost:        floatPtr(12.00),
			Images:      models.ImagesData{"https://images.unsplash.com/photo-1467003909585-2f8a72700288?q=80&w=1000&auto=format&fit=crop", "https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?q=80&w=1000&auto=format&fit=crop"},
			Ingredients: models.IngredientsData{"atlantic salmon", "seasonal vegetables", "lemon", "herbs", "butter", "olive oil"},
			Allergens:   models.AllergensData{"fish", "dairy"},
			NutritionalInfo: models.NutritionalInfoData{
				Calories: 420,
				Protein:  35.0,
				Carbs:    8.0,
				Fat:      28.0,
				Fiber:    3.5,
				Sodium:   380.0,
			},
			PreparationTime: intPtr(20),
			IsAvailable:     true,
			IsVegetarian:    false,
			IsVegan:         false,
			IsGlutenFree:    true,
			IsSpicy:         false,
			SpiceLevel:      0,
			Tags:            models.TagsData{"signature", "healthy", "protein-rich"},
		},
		{
			BranchID:    branch.ID,
			CategoryID:  &categories[1].ID, // Main Courses
			Name:        "Pad Thai",
			Slug:        "pad-thai",
			Description: "Traditional Thai stir-fried rice noodles with shrimp, tofu, bean sprouts, and our authentic tamarind sauce",
			Price:       18.99,
			Cost:        floatPtr(8.50),
			Images:      models.ImagesData{"https://images.unsplash.com/photo-**********-0f31657def5e?q=80&w=1000&auto=format&fit=crop", "https://images.unsplash.com/photo-1582878826629-29b7ad1cdc43?q=80&w=1000&auto=format&fit=crop"},
			Ingredients: models.IngredientsData{"rice noodles", "shrimp", "tofu", "bean sprouts", "eggs", "tamarind sauce", "fish sauce", "palm sugar", "peanuts", "lime"},
			Allergens:   models.AllergensData{"shellfish", "soy", "eggs", "peanuts", "fish"},
			NutritionalInfo: models.NutritionalInfoData{
				Calories: 380,
				Protein:  22.0,
				Carbs:    45.0,
				Fat:      12.0,
				Fiber:    3.8,
				Sodium:   890.0,
			},
			PreparationTime: intPtr(15),
			IsAvailable:     true,
			IsVegetarian:    false,
			IsVegan:         false,
			IsGlutenFree:    true,
			IsSpicy:         true,
			SpiceLevel:      2,
			Tags:            models.TagsData{"signature", "traditional", "spicy", "popular"},
		},
		{
			BranchID:    branch.ID,
			CategoryID:  &categories[2].ID, // Desserts
			Name:        "Chocolate Lava Cake",
			Slug:        "chocolate-lava-cake",
			Description: "Warm chocolate cake with a molten center, served with vanilla ice cream and fresh berries",
			Price:       8.99,
			Cost:        floatPtr(3.50),
			Images:      models.ImagesData{"https://images.unsplash.com/photo-1606313564200-e75d5e30476c?q=80&w=1000&auto=format&fit=crop", "https://images.unsplash.com/photo-1578985545062-69928b1d9587?q=80&w=1000&auto=format&fit=crop"},
			Ingredients: models.IngredientsData{"dark chocolate", "butter", "eggs", "sugar", "flour", "vanilla ice cream", "mixed berries"},
			Allergens:   models.AllergensData{"dairy", "eggs", "gluten"},
			NutritionalInfo: models.NutritionalInfoData{
				Calories: 450,
				Protein:  8.0,
				Carbs:    52.0,
				Fat:      24.0,
				Fiber:    4.0,
				Sodium:   180.0,
			},
			PreparationTime: intPtr(12),
			IsAvailable:     true,
			IsVegetarian:    true,
			IsVegan:         false,
			IsGlutenFree:    false,
			IsSpicy:         false,
			SpiceLevel:      0,
			Tags:            models.TagsData{"popular", "sweet", "indulgent", "warm"},
		},
		{
			BranchID:    branch.ID,
			CategoryID:  &categories[3].ID, // Beverages
			Name:        "Thai Iced Tea",
			Slug:        "thai-iced-tea",
			Description: "Traditional Thai tea with condensed milk, served over ice with a creamy finish",
			Price:       4.99,
			Cost:        floatPtr(1.50),
			Images:      models.ImagesData{"https://images.unsplash.com/photo-1571934811356-5cc061b6821f?q=80&w=1000&auto=format&fit=crop"},
			Ingredients: models.IngredientsData{"thai tea leaves", "condensed milk", "evaporated milk", "sugar", "ice"},
			Allergens:   models.AllergensData{"dairy"},
			NutritionalInfo: models.NutritionalInfoData{
				Calories: 180,
				Protein:  4.0,
				Carbs:    32.0,
				Fat:      4.5,
				Fiber:    0.0,
				Sodium:   65.0,
			},
			PreparationTime: intPtr(3),
			IsAvailable:     true,
			IsVegetarian:    true,
			IsVegan:         false,
			IsGlutenFree:    true,
			IsSpicy:         false,
			SpiceLevel:      0,
			Tags:            models.TagsData{"traditional", "refreshing", "sweet", "cold"},
		},
	}

	for _, item := range menuItems {
		if err := db.Create(item).Error; err != nil {
			return err
		}
	}

	// Create sample table areas
	areas := []*models.TableArea{
		{
			BranchID:    branch.ID,
			Name:        "Dining Area",
			Description: "Main dining room",
			Color:       "#8a745c",
			IsActive:    true,
		},
		{
			BranchID:    branch.ID,
			Name:        "Outdoor Patio",
			Description: "Outdoor seating area",
			Color:       "#6b8e23",
			IsActive:    true,
		},
	}

	for _, area := range areas {
		if err := db.Create(area).Error; err != nil {
			return err
		}
	}

	// Get the created areas for table creation
	var diningArea, patioArea models.TableArea
	if err := db.Where("branch_id = ? AND name = ?", branch.ID, "Dining Area").First(&diningArea).Error; err != nil {
		return err
	}
	if err := db.Where("branch_id = ? AND name = ?", branch.ID, "Outdoor Patio").First(&patioArea).Error; err != nil {
		return err
	}

	// Create sample tables
	tables := []*models.Table{
		// Dining Area Tables
		{
			BranchID: branch.ID,
			AreaID:   &diningArea.ID,
			Name:     "Table 1",
			Number:   1,
			Capacity: 2,
			Status:   "available",
			Position: models.PositionData{X: 100, Y: 100},
			Shape:    "square",
			Size:     models.SizeData{Width: 80, Height: 80},
			IsActive: true,
		},
		{
			BranchID: branch.ID,
			AreaID:   &diningArea.ID,
			Name:     "Table 2",
			Number:   2,
			Capacity: 4,
			Status:   "occupied",
			Position: models.PositionData{X: 200, Y: 100},
			Shape:    "round",
			Size:     models.SizeData{Width: 100, Height: 100},
			IsActive: true,
		},
		{
			BranchID: branch.ID,
			AreaID:   &diningArea.ID,
			Name:     "Table 3",
			Number:   3,
			Capacity: 6,
			Status:   "reserved",
			Position: models.PositionData{X: 300, Y: 100},
			Shape:    "rectangle",
			Size:     models.SizeData{Width: 120, Height: 80},
			IsActive: true,
		},
		{
			BranchID: branch.ID,
			AreaID:   &diningArea.ID,
			Name:     "Table 4",
			Number:   4,
			Capacity: 4,
			Status:   "available",
			Position: models.PositionData{X: 100, Y: 200},
			Shape:    "square",
			Size:     models.SizeData{Width: 80, Height: 80},
			IsActive: true,
		},
		{
			BranchID: branch.ID,
			AreaID:   &diningArea.ID,
			Name:     "Table 5",
			Number:   5,
			Capacity: 2,
			Status:   "cleaning",
			Position: models.PositionData{X: 200, Y: 200},
			Shape:    "round",
			Size:     models.SizeData{Width: 80, Height: 80},
			IsActive: true,
		},
		{
			BranchID: branch.ID,
			AreaID:   &diningArea.ID,
			Name:     "Table 6",
			Number:   6,
			Capacity: 8,
			Status:   "available",
			Position: models.PositionData{X: 300, Y: 200},
			Shape:    "rectangle",
			Size:     models.SizeData{Width: 140, Height: 100},
			IsActive: true,
		},
		// Outdoor Patio Tables
		{
			BranchID: branch.ID,
			AreaID:   &patioArea.ID,
			Name:     "Patio Table 1",
			Number:   7,
			Capacity: 4,
			Status:   "available",
			Position: models.PositionData{X: 100, Y: 100},
			Shape:    "round",
			Size:     models.SizeData{Width: 100, Height: 100},
			IsActive: true,
		},
		{
			BranchID: branch.ID,
			AreaID:   &patioArea.ID,
			Name:     "Patio Table 2",
			Number:   8,
			Capacity: 6,
			Status:   "occupied",
			Position: models.PositionData{X: 250, Y: 100},
			Shape:    "rectangle",
			Size:     models.SizeData{Width: 120, Height: 80},
			IsActive: true,
		},
		{
			BranchID: branch.ID,
			AreaID:   &patioArea.ID,
			Name:     "Patio Table 3",
			Number:   9,
			Capacity: 2,
			Status:   "available",
			Position: models.PositionData{X: 100, Y: 200},
			Shape:    "square",
			Size:     models.SizeData{Width: 80, Height: 80},
			IsActive: true,
		},
		{
			BranchID: branch.ID,
			AreaID:   &patioArea.ID,
			Name:     "Patio Table 4",
			Number:   10,
			Capacity: 4,
			Status:   "reserved",
			Position: models.PositionData{X: 250, Y: 200},
			Shape:    "round",
			Size:     models.SizeData{Width: 100, Height: 100},
			IsActive: true,
		},
	}

	for _, table := range tables {
		if err := db.Create(table).Error; err != nil {
			return err
		}
	}

	// Create sample reservations
	reservations := []*models.Reservation{
		{
			BranchID:        branch.ID,
			CustomerName:    "John Smith",
			CustomerPhone:   "+1234567890",
			CustomerEmail:   "<EMAIL>",
			PartySize:       4,
			ReservationDate: time.Now().AddDate(0, 0, 0), // Today
			ReservationTime: time.Now().Add(2 * time.Hour),
			Duration:        120,
			TableID:         &tables[1].ID, // Table 2
			Status:          "confirmed",
			SpecialRequests: "Window seat preferred",
			Source:          "website",
		},
		{
			BranchID:        branch.ID,
			CustomerName:    "Sarah Johnson",
			CustomerPhone:   "+1234567891",
			CustomerEmail:   "<EMAIL>",
			PartySize:       2,
			ReservationDate: time.Now().AddDate(0, 0, 0), // Today
			ReservationTime: time.Now().Add(4 * time.Hour),
			Duration:        90,
			TableID:         &tables[0].ID, // Table 1
			Status:          "confirmed",
			SpecialRequests: "Anniversary dinner",
			Source:          "phone",
		},
		{
			BranchID:        branch.ID,
			CustomerName:    "Mike Wilson",
			CustomerPhone:   "+1234567892",
			CustomerEmail:   "<EMAIL>",
			PartySize:       6,
			ReservationDate: time.Now().AddDate(0, 0, 1), // Tomorrow
			ReservationTime: time.Now().AddDate(0, 0, 1).Add(6 * time.Hour),
			Duration:        150,
			TableID:         &tables[2].ID, // Table 3
			Status:          "pending",
			SpecialRequests: "Birthday celebration",
			Source:          "app",
		},
		{
			BranchID:        branch.ID,
			CustomerName:    "Emily Davis",
			CustomerPhone:   "+1234567893",
			CustomerEmail:   "<EMAIL>",
			PartySize:       4,
			ReservationDate: time.Now().AddDate(0, 0, 1), // Tomorrow
			ReservationTime: time.Now().AddDate(0, 0, 1).Add(7 * time.Hour),
			Duration:        120,
			TableID:         &tables[6].ID, // Patio Table 1
			Status:          "confirmed",
			SpecialRequests: "Outdoor seating",
			Source:          "website",
		},
	}

	for _, reservation := range reservations {
		if err := db.Create(reservation).Error; err != nil {
			return err
		}
	}

	// Seed analytics data
	if err := SeedAnalyticsData(db); err != nil {
		return err
	}

	// Seed review data
	if err := seedReviewData(db, shop.ID, branch.ID); err != nil {
		return err
	}

	// Create OAuth user and associate with shop
	if err := createOAuthUser(db); err != nil {
		return err
	}

	return nil
}

// Helper function to create int pointer
func intPtr(i int) *int {
	return &i
}

// Helper function to create float64 pointer
func floatPtr(f float64) *float64 {
	return &f
}

// InitializeTest initializes an in-memory SQLite database for testing
func InitializeTest() (*gorm.DB, error) {
	// For testing, we'll use SQLite in-memory database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		return nil, err
	}

	return db, nil
}

// MigrateTest runs database migrations for testing (without PostgreSQL extensions)
func MigrateTest(db *gorm.DB) error {
	// Auto-migrate all models (SQLite doesn't need UUID extension)
	err := db.AutoMigrate(
		&models.User{},
		&models.Role{},
		&models.MenuCategory{},
		&models.MenuItem{},
		&models.Order{},
		&models.OrderItem{},
		&models.Payment{},
		&models.Reservation{},
		&models.Table{},
		&models.TableArea{},
		&models.Floor{},
		&models.Review{},

		// New models for services, shops, and campaigns
		&models.Shop{},
		&models.ShopBranch{},
		&models.Service{},
		&models.Staff{},
		&models.Appointment{},
		&models.StaffService{},
		&models.ServiceAvailability{},
		&models.CommunicationTemplate{},
		&models.CampaignSegment{},
		&models.CommunicationCampaign{},
		&models.CommunicationAnalytics{},
	)
	if err != nil {
		return err
	}

	// Create basic indexes (SQLite compatible)
	if err := createTestIndexes(db); err != nil {
		return err
	}

	return nil
}

// createTestIndexes creates basic database indexes for testing
func createTestIndexes(db *gorm.DB) error {
	indexes := []string{
		// Basic indexes that work with SQLite
		"CREATE INDEX IF NOT EXISTS idx_orders_branch_status ON orders(branch_id, status)",
		"CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at)",
		"CREATE INDEX IF NOT EXISTS idx_menu_items_branch_category ON menu_items(branch_id, category_id)",
		"CREATE INDEX IF NOT EXISTS idx_reservations_branch_date ON reservations(branch_id, reservation_date)",
		"CREATE INDEX IF NOT EXISTS idx_reviews_branch_rating ON reviews(branch_id, rating)",
		"CREATE INDEX IF NOT EXISTS idx_tables_branch_status ON tables(branch_id, status)",
		"CREATE INDEX IF NOT EXISTS idx_users_branch_status ON users(branch_id, status)",
	}

	for _, index := range indexes {
		if err := db.Exec(index).Error; err != nil {
			return err
		}
	}

	return nil
}

// seedShopData creates shop data that matches the frontend expectations
func seedShopData(db *gorm.DB) error {
	// Check if shop data already exists
	var shopCount int64
	db.Model(&models.Shop{}).Where("slug = ?", "thai-delight").Count(&shopCount)

	// Check if branches exist
	var branchCount int64
	db.Model(&models.ShopBranch{}).
		Joins("JOIN shops ON shop_branches.shop_id = shops.id").
		Where("shops.slug = ?", "thai-delight").
		Count(&branchCount)

	if shopCount > 0 && branchCount > 0 {
		return nil // Shop and branches already exist
	}

	// Create a temporary user first to satisfy the foreign key constraint
	var tempUser models.User
	err := db.Where("email = ?", "<EMAIL>").First(&tempUser).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Create a temporary system user with minimal required fields
			tempUser = models.User{
				Email:     "<EMAIL>",
				FirstName: "System",
				LastName:  "User",
				ShopID:    uuid.New(), // Temporary shop ID
				RoleID:    uuid.New(), // Temporary role ID
				Status:    models.UserStatusActive,
			}

			// Set password
			if err := tempUser.SetPassword("temp123"); err != nil {
				return err
			}

			if err := db.Create(&tempUser).Error; err != nil {
				return err
			}
		} else {
			return err
		}
	}

	// Get or create Thai Delight shop
	var thaiDelightShop models.Shop
	err = db.Where("slug = ?", "thai-delight").First(&thaiDelightShop).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Create new shop with temporary owner
			thaiDelightShop = models.Shop{
				OwnerID:     tempUser.ID,
				Name:        "Thai Delight",
				Slug:        "thai-delight",
				Description: "Authentic Thai cuisine in a cozy atmosphere",
				ShopType:    "restaurant",
				Email:       "<EMAIL>",
				Phone:       "+66 2 123 4567",
				Website:     "https://thaidelight.com",
				Logo:        "https://images.unsplash.com/photo-1559339352-11d035aa65de?q=80&w=1974&auto=format&fit=crop",
				Address: models.Address{
					Street:  "123 Thai Street",
					City:    "Bangkok",
					State:   "Bangkok",
					ZipCode: "10110",
					Country: "Thailand",
				},
				CuisineType: "Thai",
				PriceRange:  "$$",
				Rating:      4.5,
				ReviewCount: 128,
				BusinessHours: map[string]string{
					"monday":    "10:00-22:00",
					"tuesday":   "10:00-22:00",
					"wednesday": "10:00-22:00",
					"thursday":  "10:00-22:00",
					"friday":    "10:00-23:00",
					"saturday":  "11:00-23:00",
					"sunday":    "11:00-22:00",
				},
				Status:     "active",
				IsVerified: true,
				IsActive:   true,
			}

			if err := db.Create(&thaiDelightShop).Error; err != nil {
				return err
			}
		} else {
			return err
		}
	}

	// Create or get Downtown branch
	var downtownBranch models.ShopBranch
	err = db.Where("shop_id = ? AND slug = ?", thaiDelightShop.ID, "downtown").First(&downtownBranch).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Create new branch
			downtownBranch = models.ShopBranch{
				ShopID: thaiDelightShop.ID,
				Name:   "Downtown",
				Slug:   "downtown",
				Email:  "<EMAIL>",
				Phone:  "+66 2 123 4567",
				Address: models.Address{
					Street:  "123 Main Street",
					City:    "Bangkok",
					State:   "Bangkok",
					ZipCode: "10110",
					Country: "Thailand",
				},
				BusinessHours: map[string]string{
					"monday":    "10:00-22:00",
					"tuesday":   "10:00-22:00",
					"wednesday": "10:00-22:00",
					"thursday":  "10:00-22:00",
					"friday":    "10:00-23:00",
					"saturday":  "11:00-23:00",
					"sunday":    "11:00-22:00",
				},
				Timezone: "Asia/Bangkok",
				Status:   "active",
				IsActive: true,
			}

			if err := db.Create(&downtownBranch).Error; err != nil {
				return err
			}
		} else {
			return err
		}
	}

	// Create or get Riverside branch
	var riversideBranch models.ShopBranch
	err = db.Where("shop_id = ? AND slug = ?", thaiDelightShop.ID, "riverside").First(&riversideBranch).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Create new branch
			riversideBranch = models.ShopBranch{
				ShopID: thaiDelightShop.ID,
				Name:   "Riverside",
				Slug:   "riverside",
				Email:  "<EMAIL>",
				Phone:  "+66 2 234 5678",
				Address: models.Address{
					Street:  "456 River Road",
					City:    "Bangkok",
					State:   "Bangkok",
					ZipCode: "10120",
					Country: "Thailand",
				},
				BusinessHours: map[string]string{
					"monday":    "10:00-22:00",
					"tuesday":   "10:00-22:00",
					"wednesday": "10:00-22:00",
					"thursday":  "10:00-22:00",
					"friday":    "10:00-23:00",
					"saturday":  "11:00-23:00",
					"sunday":    "11:00-22:00",
				},
				Timezone: "Asia/Bangkok",
				Status:   "active",
				IsActive: true,
			}

			if err := db.Create(&riversideBranch).Error; err != nil {
				return err
			}
		} else {
			return err
		}
	}

	return nil
}

// seedAdditionalShopData creates additional shop data for testing different URL patterns
func seedAdditionalShopData(db *gorm.DB) error {
	// Check if additional shop data already exists
	var shopCount int64
	db.Model(&models.Shop{}).Where("slug = ?", "weerawat-poseeya").Count(&shopCount)

	if shopCount > 0 {
		return nil // Additional shop already exists
	}

	// Create temporary user for shop ownership
	var tempUser models.User
	err := db.Where("email = ?", "<EMAIL>").First(&tempUser).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Create temporary user
			tempUser = models.User{
				Email:        "<EMAIL>",
				FirstName:    "Temp",
				LastName:     "User",
				PasswordHash: "$2a$10$dummy.hash.for.temp.user.placeholder.value",
				Status:       "active",
			}
			if err := db.Create(&tempUser).Error; err != nil {
				return err
			}
		} else {
			return err
		}
	}

	// Create Weerawat Poseeya shop
	weerawatShop := models.Shop{
		OwnerID:     tempUser.ID,
		Name:        "Weerawat Poseeya Restaurant",
		Slug:        "weerawat-poseeya",
		Description: "Traditional Thai restaurant with authentic flavors and modern presentation",
		ShopType:    "restaurant",
		Email:       "<EMAIL>",
		Phone:       "+66 2 987 6543",
		Website:     "https://weerawat-poseeya.com",
		Logo:        "https://images.unsplash.com/photo-1559339352-11d035aa65de?q=80&w=1974&auto=format&fit=crop",
		Address: models.Address{
			Street:  "789 Sukhumvit Road",
			City:    "Bangkok",
			State:   "Bangkok",
			ZipCode: "10110",
			Country: "Thailand",
		},
		CuisineType: "Thai",
		PriceRange:  "$$$",
		Rating:      4.6,
		ReviewCount: 127,
		BusinessHours: map[string]string{
			"monday":    "11:00-22:00",
			"tuesday":   "11:00-22:00",
			"wednesday": "11:00-22:00",
			"thursday":  "11:00-22:00",
			"friday":    "11:00-23:00",
			"saturday":  "11:00-23:00",
			"sunday":    "11:00-22:00",
		},
		Status:     "active",
		IsVerified: true,
		IsActive:   true,
	}

	if err := db.Create(&weerawatShop).Error; err != nil {
		return err
	}

	// Create Posriya branch
	posriyaBranch := models.ShopBranch{
		ShopID: weerawatShop.ID,
		Name:   "Posriya",
		Slug:   "posriya",
		Email:  "<EMAIL>",
		Phone:  "+66 2 987 6544",
		Address: models.Address{
			Street:  "789 Sukhumvit Road, Soi 15",
			City:    "Bangkok",
			State:   "Bangkok",
			ZipCode: "10110",
			Country: "Thailand",
		},
		BusinessHours: map[string]string{
			"monday":    "11:00-22:00",
			"tuesday":   "11:00-22:00",
			"wednesday": "11:00-22:00",
			"thursday":  "11:00-22:00",
			"friday":    "11:00-23:00",
			"saturday":  "11:00-23:00",
			"sunday":    "11:00-22:00",
		},
		Timezone: "Asia/Bangkok",
		Status:   "active",
		IsActive: true,
	}

	if err := db.Create(&posriyaBranch).Error; err != nil {
		return err
	}

	// Seed menu items for the new branch
	if err := seedMenuItems(db, weerawatShop.ID.String(), posriyaBranch.ID.String()); err != nil {
		return err
	}

	// Seed analytics data for the new branch
	if err := seedAnalyticsForBranch(db, posriyaBranch.ID); err != nil {
		return err
	}

	// Seed comprehensive order data for the new branch
	if err := seedOrdersForBranch(db, weerawatShop.ID, posriyaBranch.ID); err != nil {
		return err
	}

	return nil
}

// seedAnalyticsForBranch creates analytics data for a specific branch
func seedAnalyticsForBranch(db *gorm.DB, branchID uuid.UUID) error {
	// Check if analytics data already exists for this branch
	var count int64
	db.Model(&models.SalesMetric{}).Where("branch_id = ?", branchID).Count(&count)
	if count > 0 {
		return nil // Analytics data already exists for this branch
	}

	// Get existing menu items for this branch
	var menuItems []models.MenuItem
	if err := db.Where("branch_id = ?", branchID).Find(&menuItems).Error; err != nil {
		return err
	}

	if len(menuItems) == 0 {
		return nil // No menu items to create analytics for
	}

	// Seed sales metrics for the last 30 days
	if err := seedSalesMetrics(db, branchID); err != nil {
		return err
	}

	// Seed menu item metrics
	if err := seedMenuItemMetrics(db, branchID, menuItems); err != nil {
		return err
	}

	// Seed customer metrics
	if err := seedCustomerMetrics(db, branchID); err != nil {
		return err
	}

	return nil
}

// seedOrdersForBranch creates comprehensive order data for a specific branch
func seedOrdersForBranch(db *gorm.DB, shopID, branchID uuid.UUID) error {
	// Check if orders already exist for this branch
	var orderCount int64
	db.Model(&models.Order{}).Where("branch_id = ?", branchID).Count(&orderCount)
	if orderCount > 0 {
		return nil // Orders already exist for this branch
	}

	// Get existing menu items for this branch
	var menuItems []models.MenuItem
	if err := db.Where("branch_id = ?", branchID).Find(&menuItems).Error; err != nil {
		return err
	}

	if len(menuItems) == 0 {
		return nil // No menu items to create orders for
	}

	// Get existing tables for this branch
	var tables []models.Table
	db.Where("branch_id = ?", branchID).Find(&tables)

	// Customer names for variety
	customerNames := []string{
		"John Smith", "Sarah Johnson", "Michael Brown", "Emily Davis", "David Wilson",
		"Lisa Anderson", "James Taylor", "Jennifer Martinez", "Robert Garcia", "Maria Rodriguez",
		"William Lee", "Jessica Thompson", "Christopher White", "Amanda Clark", "Daniel Lewis",
		"Ashley Walker", "Matthew Hall", "Stephanie Allen", "Anthony Young", "Michelle King",
		"Mark Wright", "Laura Lopez", "Steven Hill", "Nicole Scott", "Kevin Green",
		"Rachel Adams", "Brian Baker", "Kimberly Nelson", "Edward Carter", "Donna Mitchell",
		"Jason Perez", "Helen Roberts", "Ryan Turner", "Carol Phillips", "Justin Campbell",
		"Sharon Parker", "Brandon Evans", "Deborah Edwards", "Aaron Collins", "Sandra Stewart",
		"Jonathan Sanchez", "Cynthia Morris", "Jeremy Rogers", "Angela Reed", "Tyler Cook",
		"Melissa Bailey", "Adam Rivera", "Brenda Cooper", "Nathan Richardson", "Amy Cox",
	}

	// Phone numbers for variety
	phoneNumbers := []string{
		"+66 81 234 5678", "+66 82 345 6789", "+66 83 456 7890", "+66 84 567 8901",
		"+66 85 678 9012", "+66 86 789 0123", "+66 87 890 1234", "+66 88 901 2345",
		"+66 89 012 3456", "+66 90 123 4567", "+66 91 234 5678", "+66 92 345 6789",
	}

	// Order statuses with realistic distribution
	orderStatuses := []struct {
		status string
		weight int
	}{
		{"completed", 60}, // 60% completed
		{"pending", 15},   // 15% pending
		{"preparing", 10}, // 10% preparing
		{"ready", 8},      // 8% ready
		{"cancelled", 7},  // 7% cancelled
	}

	// Order types with realistic distribution
	orderTypes := []struct {
		orderType string
		weight    int
	}{
		{"dine-in", 70},  // 70% dine-in
		{"takeout", 20},  // 20% takeout
		{"delivery", 10}, // 10% delivery
	}

	// Payment methods
	paymentMethods := []string{"cash", "credit_card", "debit_card", "digital_wallet"}

	now := time.Now()

	// Create orders for the last 7 days to have enough data for testing
	for i := 0; i < 7; i++ {
		date := now.AddDate(0, 0, -i)
		dayStart := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())

		// Determine number of orders for this day
		baseOrdersPerDay := 5 + rand.Intn(10) // 5-15 orders per day

		// Weekend boost
		if date.Weekday() == time.Saturday || date.Weekday() == time.Sunday {
			baseOrdersPerDay = int(float64(baseOrdersPerDay) * 1.4)
		}

		// Create orders throughout the day
		for orderIndex := 0; orderIndex < baseOrdersPerDay; orderIndex++ {
			// Random time during business hours (11:00 - 22:00)
			hour := 11 + rand.Intn(11)
			minute := rand.Intn(60)
			orderTime := dayStart.Add(time.Duration(hour)*time.Hour + time.Duration(minute)*time.Minute)

			// Select random customer
			customerName := customerNames[rand.Intn(len(customerNames))]
			customerPhone := phoneNumbers[rand.Intn(len(phoneNumbers))]

			// Select order status based on weight
			var selectedStatus string
			totalWeight := 0
			for _, status := range orderStatuses {
				totalWeight += status.weight
			}
			randomWeight := rand.Intn(totalWeight)
			currentWeight := 0
			for _, status := range orderStatuses {
				currentWeight += status.weight
				if randomWeight < currentWeight {
					selectedStatus = status.status
					break
				}
			}

			// Select order type based on weight
			var selectedType string
			totalTypeWeight := 0
			for _, orderType := range orderTypes {
				totalTypeWeight += orderType.weight
			}
			randomTypeWeight := rand.Intn(totalTypeWeight)
			currentTypeWeight := 0
			for _, orderType := range orderTypes {
				currentTypeWeight += orderType.weight
				if randomTypeWeight < currentTypeWeight {
					selectedType = orderType.orderType
					break
				}
			}

			// Select table for dine-in orders
			var tableID *uuid.UUID
			if selectedType == "dine-in" && len(tables) > 0 {
				table := tables[rand.Intn(len(tables))]
				tableID = &table.ID
			}

			// Create order
			order := &models.Order{
				BranchID:      branchID,
				OrderNumber:   fmt.Sprintf("ORD-%s-%04d", date.Format("20060102"), orderIndex+1),
				CustomerName:  customerName,
				CustomerPhone: customerPhone,
				TableID:       tableID,
				Status:        selectedStatus,
				Type:          selectedType,
				PaymentMethod: paymentMethods[rand.Intn(len(paymentMethods))],
				PaymentStatus: func() string {
					if selectedStatus == "completed" {
						return "paid"
					} else if selectedStatus == "cancelled" {
						return "refunded"
					}
					return "pending"
				}(),
				EstimatedTime: func() *int {
					if selectedStatus == "pending" || selectedStatus == "preparing" {
						time := 15 + rand.Intn(30) // 15-45 minutes
						return &time
					}
					return nil
				}(),
			}

			// Set completion times based on status
			switch selectedStatus {
			case "completed":
				completedTime := orderTime.Add(time.Duration(20+rand.Intn(40)) * time.Minute)
				order.CompletedAt = &completedTime
				servedTime := completedTime.Add(-5 * time.Minute)
				order.ServedAt = &servedTime
			case "cancelled":
				cancelledTime := orderTime.Add(time.Duration(5+rand.Intn(15)) * time.Minute)
				order.CancelledAt = &cancelledTime
				order.CancellationReason = []string{
					"Customer requested cancellation",
					"Kitchen issue",
					"Ingredient unavailable",
					"Customer no-show",
				}[rand.Intn(4)]
			case "ready":
				servedTime := orderTime.Add(time.Duration(20+rand.Intn(20)) * time.Minute)
				order.ServedAt = &servedTime
			}

			if err := db.Create(order).Error; err != nil {
				return err
			}

			// Update timestamps manually
			db.Model(order).Updates(map[string]interface{}{
				"created_at": orderTime,
				"updated_at": orderTime,
			})

			// Create order items (1-3 items per order)
			numItems := 1 + rand.Intn(2)
			var orderSubtotal float64

			for itemIndex := 0; itemIndex < numItems; itemIndex++ {
				menuItem := menuItems[rand.Intn(len(menuItems))]
				quantity := 1 + rand.Intn(2) // 1-2 quantity

				orderItem := &models.OrderItem{
					OrderID:    order.ID,
					MenuItemID: &menuItem.ID,
					Name:       menuItem.Name,
					Price:      menuItem.Price,
					Quantity:   quantity,
					Total:      menuItem.Price * float64(quantity),
					Status:     selectedStatus,
				}

				if err := db.Create(orderItem).Error; err != nil {
					return err
				}

				orderSubtotal += orderItem.Total
			}

			// Update order totals
			order.Subtotal = orderSubtotal
			order.Tax = orderSubtotal * 0.08 // 8% tax
			order.Total = order.Subtotal + order.Tax

			// Add tip for completed orders (10-20% chance)
			if selectedStatus == "completed" && rand.Float64() < 0.15 {
				order.Tip = order.Subtotal * (0.10 + rand.Float64()*0.10) // 10-20% tip
				order.Total += order.Tip
			}

			if err := db.Save(order).Error; err != nil {
				return err
			}
		}
	}

	return nil
}

// seedStaffData creates demo staff members
func seedStaffData(db *gorm.DB, shopID, branchID uuid.UUID) error {
	// Check if staff already exists
	var staffCount int64
	db.Model(&models.User{}).Where("shop_id = ?", shopID).Count(&staffCount)

	if staffCount > 0 {
		return nil // Staff already exists
	}

	// Create default roles first
	managerRole := models.Role{
		ShopID:      shopID,
		Name:        "Manager",
		Description: "Restaurant manager with full access",
		Permissions: models.PermissionsData{
			models.PermissionManageStaff,
			models.PermissionViewStaff,
			models.PermissionManageOrders,
			models.PermissionViewOrders,
			models.PermissionManageMenu,
			models.PermissionViewMenu,
			models.PermissionManageReservations,
			models.PermissionViewReservations,
			models.PermissionManageTables,
			models.PermissionViewTables,
			models.PermissionViewReports,
			models.PermissionManageSettings,
			models.PermissionViewSettings,
			models.PermissionViewDashboard,
		},
		IsActive: true,
	}

	serverRole := models.Role{
		ShopID:      shopID,
		Name:        "Server",
		Description: "Restaurant server",
		Permissions: models.PermissionsData{
			models.PermissionViewOrders,
			models.PermissionManageOrders,
			models.PermissionViewMenu,
			models.PermissionViewReservations,
			models.PermissionManageReservations,
			models.PermissionViewTables,
			models.PermissionManageTables,
			models.PermissionViewDashboard,
		},
		IsActive: true,
	}

	chefRole := models.Role{
		ShopID:      shopID,
		Name:        "Chef",
		Description: "Kitchen chef",
		Permissions: models.PermissionsData{
			models.PermissionViewOrders,
			models.PermissionViewMenu,
			models.PermissionManageMenu,
			models.PermissionViewDashboard,
		},
		IsActive: true,
	}

	// Create roles
	if err := db.Create(&managerRole).Error; err != nil {
		return err
	}
	if err := db.Create(&serverRole).Error; err != nil {
		return err
	}
	if err := db.Create(&chefRole).Error; err != nil {
		return err
	}

	// Create demo staff members
	staffMembers := []models.User{
		{
			ShopID:     shopID,
			BranchID:   &branchID,
			Email:      "<EMAIL>",
			FirstName:  "John",
			LastName:   "Manager",
			Phone:      "+66 81 234 5678",
			RoleID:     managerRole.ID,
			Position:   "Restaurant Manager",
			Department: "Management",
			EmployeeID: "EMP001",
			HireDate:   time.Now().AddDate(0, -6, 0), // 6 months ago
			Salary:     &[]float64{45000}[0],
			Status:     "active",
			AvatarURL:  "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=1000&auto=format&fit=crop",
		},
		{
			ShopID:     shopID,
			BranchID:   &branchID,
			Email:      "<EMAIL>",
			FirstName:  "Sarah",
			LastName:   "Johnson",
			Phone:      "+66 82 345 6789",
			RoleID:     serverRole.ID,
			Position:   "Senior Server",
			Department: "Service",
			EmployeeID: "EMP002",
			HireDate:   time.Now().AddDate(0, -4, 0), // 4 months ago
			HourlyRate: &[]float64{180}[0],
			Status:     "active",
			AvatarURL:  "https://images.unsplash.com/photo-1494790108755-2616b612b786?q=80&w=1000&auto=format&fit=crop",
		},
		{
			ShopID:     shopID,
			BranchID:   &branchID,
			Email:      "<EMAIL>",
			FirstName:  "Mike",
			LastName:   "Chen",
			Phone:      "+66 83 456 7890",
			RoleID:     chefRole.ID,
			Position:   "Head Chef",
			Department: "Kitchen",
			EmployeeID: "EMP003",
			HireDate:   time.Now().AddDate(-1, 0, 0), // 1 year ago
			Salary:     &[]float64{38000}[0],
			Status:     "active",
			AvatarURL:  "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=1000&auto=format&fit=crop",
		},
		{
			ShopID:     shopID,
			BranchID:   &branchID,
			Email:      "<EMAIL>",
			FirstName:  "Anna",
			LastName:   "Williams",
			Phone:      "+66 84 567 8901",
			RoleID:     serverRole.ID,
			Position:   "Server",
			Department: "Service",
			EmployeeID: "EMP004",
			HireDate:   time.Now().AddDate(0, -2, 0), // 2 months ago
			HourlyRate: &[]float64{160}[0],
			Status:     "active",
			AvatarURL:  "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=1000&auto=format&fit=crop",
		},
	}

	// Set passwords and create staff members
	for i := range staffMembers {
		if err := staffMembers[i].SetPassword("password123"); err != nil {
			return err
		}

		if err := db.Create(&staffMembers[i]).Error; err != nil {
			return err
		}
	}

	return nil
}

// createOAuthUser creates an OAuth user and associates them with the Thai Delight shop
func createOAuthUser(db *gorm.DB) error {
	// OAuth user email from the frontend logs
	oauthEmail := "<EMAIL>"

	// Check if OAuth user already exists by email (since OAuth IDs are strings)
	var existingUser models.User
	err := db.Where("email = ?", oauthEmail).First(&existingUser).Error
	if err == nil {
		// User already exists, no need to create
		return nil
	}

	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return err // Some other error occurred
	}

	// Get the Thai Delight shop
	var shop models.Shop
	if err := db.Where("slug = ?", "thai-delight").First(&shop).Error; err != nil {
		return err
	}

	// Get the downtown branch
	var branch models.ShopBranch
	if err := db.Where("shop_id = ? AND slug = ?", shop.ID, "downtown").First(&branch).Error; err != nil {
		return err
	}

	// Get or create admin role
	var adminRole models.Role
	err = db.Where("shop_id = ? AND name = ?", shop.ID, "Admin").First(&adminRole).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Create admin role
			adminRole = models.Role{
				ShopID:      shop.ID,
				Name:        "Admin",
				Description: "Full administrative access",
				Permissions: models.PermissionsData{
					models.PermissionViewDashboard,
					models.PermissionManageOrders,
					models.PermissionViewOrders,
					models.PermissionManageMenu,
					models.PermissionViewMenu,
					models.PermissionManageStaff,
					models.PermissionViewStaff,
					models.PermissionManageReservations,
					models.PermissionViewReservations,
					models.PermissionManageTables,
					models.PermissionViewTables,
					models.PermissionManageReviews,
					models.PermissionViewReviews,
					models.PermissionViewReports,
					models.PermissionManageSettings,
					models.PermissionViewSettings,
				},
				IsActive: true,
			}

			if err := db.Create(&adminRole).Error; err != nil {
				return err
			}
		} else {
			return err
		}
	}

	// Create OAuth user (GORM will auto-generate UUID)
	oauthUser := models.User{
		ShopID:     shop.ID,
		BranchID:   &branch.ID,
		Email:      oauthEmail,
		FirstName:  "Weerawat",
		LastName:   "Poseeya",
		Phone:      "+66 81 234 5678",
		RoleID:     adminRole.ID,
		Position:   "Owner",
		Department: "Management",
		EmployeeID: "OWNER001",
		Status:     models.UserStatusActive,
		HireDate:   time.Now(),
	}

	// Set a default password (won't be used for OAuth login)
	if err := oauthUser.SetPassword("oauth123"); err != nil {
		return err
	}

	// Create the user
	if err := db.Create(&oauthUser).Error; err != nil {
		return err
	}

	// Update the shop to be owned by the OAuth user
	if err := db.Model(&shop).Update("owner_id", oauthUser.ID).Error; err != nil {
		return err
	}

	return nil
}

// seedReviewData creates sample review data for development
func seedReviewData(db *gorm.DB, shopID, branchID uuid.UUID) error {
	// Check if review data already exists
	var reviewCount int64
	db.Model(&models.Review{}).Where("branch_id = ?", branchID).Count(&reviewCount)
	if reviewCount > 0 {
		return nil // Review data already exists
	}

	// Sample customer names and avatars
	customers := []struct {
		name   string
		email  string
		avatar string
	}{
		{"Sarah Johnson", "<EMAIL>", "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face"},
		{"Michael Chen", "<EMAIL>", "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"},
		{"Emily Rodriguez", "<EMAIL>", "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face"},
		{"David Kim", "<EMAIL>", "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"},
		{"Jessica Thompson", "<EMAIL>", "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face"},
		{"Alex Martinez", "<EMAIL>", "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face"},
		{"Lisa Wang", "<EMAIL>", "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face"},
		{"James Wilson", "<EMAIL>", "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face"},
		{"Maria Garcia", "<EMAIL>", "https://images.unsplash.com/photo-1557804506-669a67965ba0?w=150&h=150&fit=crop&crop=face"},
		{"Robert Brown", "<EMAIL>", "https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face"},
	}

	// Sample review comments by rating
	reviewComments := map[int][]struct {
		title   string
		comment string
		tags    []string
	}{
		5: {
			{"Absolutely Amazing!", "The food was incredible and the service was outstanding. Every dish was perfectly prepared and the staff was so attentive. Will definitely be coming back!", []string{"excellent", "outstanding", "perfect"}},
			{"Best Thai Food Ever!", "This place exceeded all my expectations. The Pad Thai was authentic and delicious, and the Tom Yum soup was the perfect balance of spicy and sour. Highly recommended!", []string{"authentic", "delicious", "recommended"}},
			{"Perfect Dining Experience", "From the moment we walked in, we were treated like family. The atmosphere is cozy and the food is absolutely divine. The mango sticky rice was the perfect ending to a perfect meal.", []string{"perfect", "cozy", "divine"}},
		},
		4: {
			{"Great Food, Good Service", "Really enjoyed our meal here. The curry was flavorful and the portions were generous. Service was friendly and efficient. Would come back again.", []string{"flavorful", "generous", "friendly"}},
			{"Solid Thai Restaurant", "Good quality Thai food with reasonable prices. The green curry was excellent and the spring rolls were crispy and fresh. Minor wait time but worth it.", []string{"quality", "reasonable", "excellent"}},
			{"Very Good Experience", "The food was tasty and well-presented. Staff was knowledgeable about the menu and made good recommendations. Nice atmosphere for a dinner out.", []string{"tasty", "knowledgeable", "nice"}},
		},
		3: {
			{"Decent Food, Average Service", "The food was okay but nothing special. Service was a bit slow and the restaurant was quite noisy. Prices are reasonable though.", []string{"okay", "slow", "noisy"}},
			{"Mixed Experience", "Some dishes were great while others were just average. The Pad See Ew was excellent but the Tom Kha was too sweet for my taste. Hit or miss.", []string{"mixed", "average", "sweet"}},
			{"Average Thai Food", "Standard Thai restaurant fare. Nothing wrong with it but nothing that stands out either. Good for a quick meal but not memorable.", []string{"standard", "quick", "average"}},
		},
		2: {
			{"Disappointing Visit", "Expected much better based on reviews. Food was bland and service was inattentive. The restaurant was also quite dirty. Won't be returning.", []string{"disappointing", "bland", "dirty"}},
			{"Not Great", "Food took forever to arrive and when it did, it was lukewarm. The flavors were off and the vegetables were overcooked. Poor value for money.", []string{"slow", "lukewarm", "overcooked"}},
		},
		1: {
			{"Terrible Experience", "Worst Thai food I've ever had. The curry was watery and flavorless, and the rice was undercooked. Service was rude and unprofessional. Avoid at all costs.", []string{"terrible", "watery", "rude"}},
		},
	}

	// Create reviews for the past 60 days
	now := time.Now()
	reviews := []*models.Review{}

	for i := 0; i < 45; i++ { // Create 45 reviews over 60 days
		// Random date in the past 60 days
		daysAgo := rand.Intn(60)
		reviewDate := now.AddDate(0, 0, -daysAgo)

		// Random customer
		customer := customers[rand.Intn(len(customers))]

		// Random rating with bias towards higher ratings (realistic distribution)
		var rating int
		randNum := rand.Float64()
		if randNum < 0.4 { // 40% chance of 5 stars
			rating = 5
		} else if randNum < 0.7 { // 30% chance of 4 stars
			rating = 4
		} else if randNum < 0.85 { // 15% chance of 3 stars
			rating = 3
		} else if randNum < 0.95 { // 10% chance of 2 stars
			rating = 2
		} else { // 5% chance of 1 star
			rating = 1
		}

		// Get random comment for this rating
		comments := reviewComments[rating]
		if len(comments) == 0 {
			continue
		}
		selectedComment := comments[rand.Intn(len(comments))]

		// Random source
		sources := []string{"google", "yelp", "internal", "facebook", "tripadvisor"}
		source := sources[rand.Intn(len(sources))]

		// Random status with bias towards approved
		var status string
		statusRand := rand.Float64()
		if statusRand < 0.8 { // 80% approved
			status = models.ReviewStatusApproved
		} else if statusRand < 0.9 { // 10% pending
			status = models.ReviewStatusPending
		} else if statusRand < 0.98 { // 8% flagged
			status = models.ReviewStatusFlagged
		} else { // 2% rejected
			status = models.ReviewStatusRejected
		}

		review := &models.Review{
			BranchID:       branchID,
			CustomerName:   customer.name,
			CustomerEmail:  customer.email,
			CustomerAvatar: customer.avatar,
			Rating:         rating,
			Title:          selectedComment.title,
			Comment:        selectedComment.comment,
			Photos:         models.PhotosData{},
			Source:         source,
			Status:         status,
			IsVerified:     rand.Float64() < 0.3, // 30% verified
			IsPublic:       true,
			Tags:           models.TagsData(selectedComment.tags),
			Sentiment:      getSentimentFromRating(rating),
		}

		// Set created_at to the review date
		review.CreatedAt = reviewDate
		review.UpdatedAt = reviewDate

		// Add response to some approved reviews (60% chance)
		if status == models.ReviewStatusApproved && rand.Float64() < 0.6 {
			responseDate := reviewDate.Add(time.Duration(rand.Intn(72)) * time.Hour) // Response within 3 days
			response := &models.ResponseData{
				Message:     getResponseMessage(rating),
				RespondedBy: "Restaurant Manager",
				RespondedAt: responseDate,
			}
			review.Response = response
		}

		reviews = append(reviews, review)
	}

	// Create all reviews
	for _, review := range reviews {
		if err := db.Create(review).Error; err != nil {
			return err
		}
	}

	return nil
}

// getSentimentFromRating determines sentiment based on rating
func getSentimentFromRating(rating int) string {
	if rating >= 4 {
		return models.ReviewSentimentPositive
	} else if rating >= 3 {
		return models.ReviewSentimentNeutral
	} else {
		return models.ReviewSentimentNegative
	}
}

// getResponseMessage generates appropriate response based on rating
func getResponseMessage(rating int) string {
	responses := map[int][]string{
		5: {
			"Thank you so much for your wonderful review! We're thrilled that you had such a great experience with us. We look forward to serving you again soon!",
			"We're absolutely delighted to hear that you enjoyed your visit! Your kind words mean the world to our team. Thank you for choosing us!",
			"What a fantastic review! We're so happy that everything exceeded your expectations. We can't wait to welcome you back!",
		},
		4: {
			"Thank you for your positive feedback! We're glad you enjoyed your meal and had a good experience. We appreciate your business!",
			"We're pleased to hear that you had a great time with us! Thank you for the kind words and for choosing our restaurant.",
			"Thanks for the lovely review! We're happy that you enjoyed the food and service. Hope to see you again soon!",
		},
		3: {
			"Thank you for your feedback. We appreciate you taking the time to share your experience and we'll work on improving the areas you mentioned.",
			"We value your honest review and will take your comments into consideration as we continue to improve our service.",
			"Thank you for dining with us. We appreciate your feedback and will use it to enhance our guests' experience.",
		},
		2: {
			"We sincerely apologize that your experience didn't meet your expectations. We take your feedback seriously and would like to make this right. Please contact us directly so we can address your concerns.",
			"We're sorry to hear about your disappointing experience. Your feedback is important to us and we're committed to improving. We'd appreciate the opportunity to discuss this further.",
		},
		1: {
			"We deeply apologize for the poor experience you had at our restaurant. This is not the standard we strive for. Please contact our management team directly so we can address these issues immediately and make this right.",
			"We're extremely sorry that we failed to meet your expectations. Your experience is unacceptable and we take full responsibility. Please reach out to us directly so we can resolve this matter.",
		},
	}

	responseList := responses[rating]
	if len(responseList) == 0 {
		return "Thank you for your feedback. We appreciate you taking the time to share your experience with us."
	}

	return responseList[rand.Intn(len(responseList))]
}
