package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"restaurant-backend/internal/services"
	"restaurant-backend/internal/types"
)

type AIGenerationHandler struct {
	aiService     *services.AIGenerationService
	storageService *services.StorageService
	logger        *logrus.Logger
}

func NewAIGenerationHandler(
	aiService *services.AIGenerationService,
	storageService *services.StorageService,
	logger *logrus.Logger,
) *AIGenerationHandler {
	return &AIGenerationHandler{
		aiService:     aiService,
		storageService: storageService,
		logger:        logger,
	}
}

// CreateJob godoc
// @Summary Create a new AI generation job
// @Description Create a new AI generation job for menu items
// @Tags ai-generation
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param job body types.CreateAIGenerationJobRequest true "AI generation job data"
// @Success 201 {object} types.AIGenerationJobResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/ai-generation/jobs [post]
func (h *AIGenerationHandler) CreateJob(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Get user ID from context
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userID, ok := userIDInterface.(uuid.UUID)
	if !ok {
		// Try to parse as string if it's not already a UUID
		if userIDStr, ok := userIDInterface.(string); ok {
			userID, err = uuid.Parse(userIDStr)
			if err != nil {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID"})
				return
			}
		} else {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID format"})
			return
		}
	}

	var req types.CreateAIGenerationJobRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data", "details": err.Error()})
		return
	}

	job, err := h.aiService.CreateJob(c.Request.Context(), branchID, userID, req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create AI generation job")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create job"})
		return
	}

	c.JSON(http.StatusCreated, job)
}

// GetJob godoc
// @Summary Get AI generation job by ID
// @Description Retrieve an AI generation job by its ID
// @Tags ai-generation
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param jobId path string true "Job ID"
// @Success 200 {object} types.AIGenerationJobResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/ai-generation/jobs/{jobId} [get]
func (h *AIGenerationHandler) GetJob(c *gin.Context) {
	jobID, err := uuid.Parse(c.Param("jobId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid job ID"})
		return
	}

	job, err := h.aiService.GetJob(c.Request.Context(), jobID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get AI generation job")
		c.JSON(http.StatusNotFound, gin.H{"error": "Job not found"})
		return
	}

	c.JSON(http.StatusOK, job)
}

// GetJobs godoc
// @Summary Get AI generation jobs for a branch
// @Description Retrieve AI generation jobs for a specific branch with filters
// @Tags ai-generation
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param type query string false "Job type filter"
// @Param status query string false "Job status filter"
// @Param page query int false "Page number"
// @Param limit query int false "Items per page"
// @Success 200 {object} types.AIGenerationJobsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/ai-generation/jobs [get]
func (h *AIGenerationHandler) GetJobs(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.AIGenerationJobFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	jobs, err := h.aiService.GetJobsByBranch(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get AI generation jobs")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get jobs"})
		return
	}

	c.JSON(http.StatusOK, jobs)
}

// PublishMenu godoc
// @Summary Publish AI generated menu
// @Description Publish AI generated menu items to the actual menu
// @Tags ai-generation
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param request body types.PublishAIGeneratedMenuRequest true "Publish menu request"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/ai-generation/publish [post]
func (h *AIGenerationHandler) PublishMenu(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var req types.PublishAIGeneratedMenuRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data", "details": err.Error()})
		return
	}

	err = h.aiService.PublishGeneratedMenu(c.Request.Context(), branchID, req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to publish AI generated menu")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to publish menu"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Menu published successfully"})
}

// UploadFile godoc
// @Summary Upload file for AI generation
// @Description Upload an image file for AI menu generation
// @Tags ai-generation
// @Accept multipart/form-data
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param type formData string true "File type (menu_image or food_image)"
// @Param file formData file true "Image file"
// @Success 200 {object} types.UploadAIFileResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/ai-generation/upload [post]
func (h *AIGenerationHandler) UploadFile(c *gin.Context) {
	shopID := c.Param("shopId")
	branchID := c.Param("branchId")

	// Get file type from form
	fileType := c.PostForm("type")
	if fileType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File type is required"})
		return
	}

	if fileType != "menu_image" && fileType != "food_image" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file type. Must be 'menu_image' or 'food_image'"})
		return
	}

	// Get uploaded file
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No file uploaded"})
		return
	}
	defer file.Close()

	// Upload to storage
	var fileURL string
	if fileType == "menu_image" {
		fileURL, err = h.storageService.UploadMenuImage(c.Request.Context(), file, header, shopID, branchID)
	} else {
		fileURL, err = h.storageService.UploadMenuImage(c.Request.Context(), file, header, shopID, branchID)
	}

	if err != nil {
		h.logger.WithError(err).Error("Failed to upload file")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to upload file"})
		return
	}

	response := types.UploadAIFileResponse{
		FileURL: fileURL,
		Message: "File uploaded successfully",
	}

	c.JSON(http.StatusOK, response)
}
